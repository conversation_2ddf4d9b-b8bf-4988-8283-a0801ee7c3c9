<?php
// Set timezone ke Asia/Jakarta
date_default_timezone_set('Asia/Jakarta');

// <PERSON><PERSON> proses dekripsi (12 jam = 43200 detik)
session_start();
if (!isset($_SESSION['decrypt_start'])) {
    $_SESSION['decrypt_start'] = time();
}

$start_time = $_SESSION['decrypt_start'];
$current_time = time();
$duration = 43200; // 12 jam dalam detik
$elapsed = $current_time - $start_time;
$remaining = max(0, $duration - $elapsed);
$progress = min(100, ($elapsed / $duration) * 100);

// <PERSON><PERSON> sudah selesai, redirect ke halaman sukses
if ($remaining <= 0) {
    header('Location: decrypt_complete.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔓 Mendekripsi Data Website...</title>
    <link rel="shortcut icon" href="https://client.indowebsolution.com/uploads/company/favicon.png" />
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #000;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            overflow: hidden;
            height: 100vh;
        }

        .terminal {
            padding: 20px;
            height: 100vh;
            overflow-y: auto;
            background: linear-gradient(45deg, #000000, #001100);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            border: 2px solid #00ff00;
            padding: 15px;
            background: rgba(0, 255, 0, 0.1);
        }

        .title {
            font-size: 24px;
            margin-bottom: 10px;
            text-shadow: 0 0 10px #00ff00;
        }

        .subtitle {
            font-size: 14px;
            opacity: 0.8;
        }

        .progress-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #00ff00;
            background: rgba(0, 255, 0, 0.05);
        }

        .progress-bar {
            width: 100%;
            height: 30px;
            background: #003300;
            border: 2px solid #00ff00;
            margin: 10px 0;
            position: relative;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00ff00, #00aa00);
            transition: width 1s ease;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 20px;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3));
            animation: shine 2s infinite;
        }

        @keyframes shine {
            0% { transform: translateX(-20px); }
            100% { transform: translateX(20px); }
        }

        .stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }

        .stat-box {
            border: 1px solid #00ff00;
            padding: 10px;
            text-align: center;
            background: rgba(0, 255, 0, 0.05);
        }

        .stat-value {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.8;
        }

        .log-section {
            margin-top: 20px;
            height: 300px;
            overflow-y: auto;
            border: 1px solid #00ff00;
            padding: 10px;
            background: rgba(0, 255, 0, 0.02);
        }

        .log-line {
            margin: 2px 0;
            opacity: 0;
            animation: fadeIn 0.5s ease forwards;
        }

        @keyframes fadeIn {
            to { opacity: 1; }
        }

        .cursor {
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        .warning {
            color: #ffaa00;
        }

        .error {
            color: #ff0000;
        }

        .success {
            color: #00ffaa;
        }

        .matrix {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            opacity: 0.1;
            z-index: -1;
        }
    </style>
</head>
<body>
    <canvas class="matrix" id="matrix"></canvas>
    
    <div class="terminal">
        <div class="header">
            <div class="title">🔓 SISTEM DEKRIPSI DATA WEBSITE</div>
            <div class="subtitle">Proses dekripsi database dan file sistem sedang berlangsung...</div>
        </div>

        <div class="progress-section">
            <div>Status: <span id="status">Memulai dekripsi...</span></div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill" style="width: <?php echo $progress; ?>%"></div>
            </div>
            <div style="text-align: center; margin-top: 10px;">
                <span id="percentage"><?php echo number_format($progress, 2); ?>%</span> selesai
            </div>
        </div>

        <div class="stats">
            <div class="stat-box">
                <div class="stat-value" id="timeRemaining">
                    <?php 
                    $hours = floor($remaining / 3600);
                    $minutes = floor(($remaining % 3600) / 60);
                    $seconds = $remaining % 60;
                    echo sprintf("%02d:%02d:%02d", $hours, $minutes, $seconds);
                    ?>
                </div>
                <div class="stat-label">Waktu Tersisa</div>
            </div>
            <div class="stat-box">
                <div class="stat-value" id="filesProcessed">0</div>
                <div class="stat-label">File Terdekripsi</div>
            </div>
        </div>

        <div class="log-section" id="logSection">
            <div class="log-line">[<?php echo date('H:i:s'); ?>] Memulai proses dekripsi sistem...</div>
            <div class="log-line">[<?php echo date('H:i:s'); ?>] Menganalisis struktur database...</div>
            <div class="log-line">[<?php echo date('H:i:s'); ?>] Memuat kunci dekripsi AES-256...</div>
        </div>
    </div>

    <script>
        // Data dari server
        const serverData = {
            startTime: <?php echo $start_time * 1000; ?>,
            duration: <?php echo $duration * 1000; ?>,
            currentProgress: <?php echo $progress; ?>
        };

        // Matrix effect
        const canvas = document.getElementById('matrix');
        const ctx = canvas.getContext('2d');
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;

        const matrix = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789@#$%^&*()*&^%+-/~{[|`]}";
        const matrixArray = matrix.split("");
        const fontSize = 10;
        const columns = canvas.width / fontSize;
        const drops = [];

        for (let x = 0; x < columns; x++) {
            drops[x] = 1;
        }

        function drawMatrix() {
            ctx.fillStyle = 'rgba(0, 0, 0, 0.04)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            ctx.fillStyle = '#00ff00';
            ctx.font = fontSize + 'px monospace';

            for (let i = 0; i < drops.length; i++) {
                const text = matrixArray[Math.floor(Math.random() * matrixArray.length)];
                ctx.fillText(text, i * fontSize, drops[i] * fontSize);

                if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {
                    drops[i] = 0;
                }
                drops[i]++;
            }
        }

        setInterval(drawMatrix, 35);

        // Update progress
        function updateProgress() {
            const now = new Date().getTime();
            const elapsed = now - serverData.startTime;
            const remaining = Math.max(0, serverData.duration - elapsed);
            const progress = Math.min(100, (elapsed / serverData.duration) * 100);

            // Update progress bar
            document.getElementById('progressFill').style.width = progress + '%';
            document.getElementById('percentage').textContent = progress.toFixed(2) + '%';

            // Update timer
            const hours = Math.floor(remaining / (1000 * 60 * 60));
            const minutes = Math.floor((remaining % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((remaining % (1000 * 60)) / 1000);
            document.getElementById('timeRemaining').textContent = 
                String(hours).padStart(2, '0') + ':' + 
                String(minutes).padStart(2, '0') + ':' + 
                String(seconds).padStart(2, '0');

            // Update files processed
            const filesProcessed = Math.floor(progress * 2.47); // Fake calculation
            document.getElementById('filesProcessed').textContent = filesProcessed;

            // Update status
            const statusEl = document.getElementById('status');
            if (progress < 25) {
                statusEl.textContent = 'Menganalisis enkripsi...';
            } else if (progress < 50) {
                statusEl.textContent = 'Memecah kunci enkripsi...';
            } else if (progress < 75) {
                statusEl.textContent = 'Mendekripsi database...';
            } else if (progress < 95) {
                statusEl.textContent = 'Memulihkan file sistem...';
            } else {
                statusEl.textContent = 'Menyelesaikan proses...';
            }

            // Redirect when complete
            if (remaining <= 0) {
                window.location.href = 'decrypt_complete.php';
            }
        }

        // Add random log messages
        const logMessages = [
            'Memproses file: config.php',
            'Dekripsi berhasil: database.sql',
            'Memulihkan: user_data.json',
            'Menganalisis: security_keys.txt',
            'Memproses: admin_panel.php',
            'Dekripsi berhasil: backup_files.zip',
            'Memulihkan: system_logs.txt',
            'Menganalisis: encryption_keys.dat'
        ];

        function addLogMessage() {
            const logSection = document.getElementById('logSection');
            const now = new Date();
            const timeStr = now.getHours().toString().padStart(2, '0') + ':' + 
                           now.getMinutes().toString().padStart(2, '0') + ':' + 
                           now.getSeconds().toString().padStart(2, '0');
            
            const message = logMessages[Math.floor(Math.random() * logMessages.length)];
            const logLine = document.createElement('div');
            logLine.className = 'log-line success';
            logLine.textContent = `[${timeStr}] ${message}`;
            
            logSection.appendChild(logLine);
            logSection.scrollTop = logSection.scrollHeight;

            // Remove old messages to prevent overflow
            if (logSection.children.length > 50) {
                logSection.removeChild(logSection.firstChild);
            }
        }

        // Update every second
        setInterval(updateProgress, 1000);
        setInterval(addLogMessage, 3000 + Math.random() * 4000); // Random interval 3-7 seconds

        // Initial update
        updateProgress();
    </script>
</body>
</html>
