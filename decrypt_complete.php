<?php
// Set timezone ke Asia/Jakarta
date_default_timezone_set('Asia/Jakarta');
session_start();

// Reset session dekripsi
unset($_SESSION['decrypt_start']);
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>✅ <PERSON><PERSON><PERSON><PERSON></title>
    <link rel="shortcut icon" href="https://client.indowebsolution.com/uploads/company/favicon.png" />
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #000000, #001100, #000000);
            color: #00ff00;
            font-family: 'Courier New', monospace;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            text-align: center;
            padding: 40px;
            border: 3px solid #00ff00;
            background: rgba(0, 255, 0, 0.1);
            border-radius: 10px;
            box-shadow: 0 0 30px rgba(0, 255, 0, 0.3);
            max-width: 600px;
            width: 90%;
        }

        .success-icon {
            font-size: 80px;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .title {
            font-size: 32px;
            margin-bottom: 20px;
            text-shadow: 0 0 15px #00ff00;
        }

        .message {
            font-size: 18px;
            margin-bottom: 30px;
            line-height: 1.6;
            opacity: 0.9;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .stat-item {
            padding: 15px;
            border: 1px solid #00ff00;
            background: rgba(0, 255, 0, 0.05);
            border-radius: 5px;
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.8;
        }

        .actions {
            margin-top: 30px;
        }

        .btn {
            display: inline-block;
            padding: 12px 25px;
            margin: 10px;
            background: transparent;
            border: 2px solid #00ff00;
            color: #00ff00;
            text-decoration: none;
            font-family: 'Courier New', monospace;
            font-size: 16px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .btn:hover {
            background: #00ff00;
            color: #000;
            box-shadow: 0 0 20px rgba(0, 255, 0, 0.5);
        }

        .completion-time {
            margin-top: 20px;
            font-size: 14px;
            opacity: 0.7;
        }

        .matrix-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            opacity: 0.1;
            z-index: -1;
        }
    </style>
</head>
<body>
    <canvas class="matrix-bg" id="matrix"></canvas>
    
    <div class="container">
        <div class="success-icon">✅</div>
        <h1 class="title">DEKRIPSI BERHASIL!</h1>
        
        <div class="message">
            Proses dekripsi data website telah selesai dengan sukses.<br>
            Semua file dan database telah berhasil dipulihkan.
        </div>

        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-value">247</div>
                <div class="stat-label">File Terdekripsi</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">12:00:00</div>
                <div class="stat-label">Waktu Proses</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">100%</div>
                <div class="stat-label">Tingkat Keberhasilan</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">AES-256</div>
                <div class="stat-label">Metode Enkripsi</div>
            </div>
        </div>

        <div class="actions">
            <a href="index.php" class="btn">Kembali ke Beranda</a>
            <button onclick="startNewDecryption()" class="btn">Mulai Dekripsi Baru</button>
        </div>

        <div class="completion-time">
            Selesai pada: <?php echo date('d F Y, H:i:s'); ?> WIB
        </div>
    </div>

    <script>
        // Matrix effect background
        const canvas = document.getElementById('matrix');
        const ctx = canvas.getContext('2d');
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;

        const matrix = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789@#$%^&*()*&^%+-/~{[|`]}";
        const matrixArray = matrix.split("");
        const fontSize = 10;
        const columns = canvas.width / fontSize;
        const drops = [];

        for (let x = 0; x < columns; x++) {
            drops[x] = 1;
        }

        function drawMatrix() {
            ctx.fillStyle = 'rgba(0, 0, 0, 0.04)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            ctx.fillStyle = '#00ff00';
            ctx.font = fontSize + 'px monospace';

            for (let i = 0; i < drops.length; i++) {
                const text = matrixArray[Math.floor(Math.random() * matrixArray.length)];
                ctx.fillText(text, i * fontSize, drops[i] * fontSize);

                if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {
                    drops[i] = 0;
                }
                drops[i]++;
            }
        }

        setInterval(drawMatrix, 35);

        // Resize canvas on window resize
        window.addEventListener('resize', () => {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        });

        function startNewDecryption() {
            if (confirm('Apakah Anda yakin ingin memulai proses dekripsi baru? Ini akan memakan waktu 12 jam lagi.')) {
                window.location.href = 'decrypt.php';
            }
        }

        // Auto redirect after 30 seconds
        setTimeout(() => {
            if (confirm('Halaman akan kembali ke beranda. Lanjutkan?')) {
                window.location.href = 'index.php';
            }
        }, 30000);
    </script>
</body>
</html>
