<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Set timezone ke Asia/Jakarta
date_default_timezone_set('Asia/Jakarta');

// Target deadline: 30 Juli 2025 jam 23:59:59 WIB
$target_date = '2025-07-30 23:59:59';
$target_timestamp = strtotime($target_date);

// Tanggal penghapusan data: 3 hari setelah deadline (2 Agustus 2025)
$deletion_date = '2025-08-02 23:59:59';
$deletion_timestamp = strtotime($deletion_date);

// Waktu server saat ini
$current_timestamp = time();

// Hitung selisih waktu
$time_remaining = $target_timestamp - $current_timestamp;
$deletion_remaining = $deletion_timestamp - $current_timestamp;

// Jika waktu sudah habis, redirect ke 404
if ($time_remaining <= 0) {
    http_response_code(404);
    echo json_encode([
        'status' => 'expired',
        'redirect' => '404.php',
        'message' => 'Waktu telah habis'
    ]);
    exit;
}

// Response JSON dengan informasi countdown
echo json_encode([
    'status' => 'active',
    'server_time' => $current_timestamp,
    'target_timestamp' => $target_timestamp,
    'deletion_timestamp' => $deletion_timestamp,
    'time_remaining' => $time_remaining,
    'deletion_remaining' => $deletion_remaining,
    'formatted_time' => date('Y-m-d H:i:s', $current_timestamp),
    'target_date' => $target_date,
    'deletion_date' => $deletion_date
]);
?>
